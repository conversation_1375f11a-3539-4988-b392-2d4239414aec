/* Skeleton Layout */
.skeleton-layout {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: var(--z-index-overlay, 1003);
  display: flex;
  background: var(--skeleton-bg, #202023);
}

.skeleton-sidebar {
  width: var(--skeleton-sidebar-width, 260px);
  background: var(--skeleton-sidebar-bg, #26262e);
  flex-shrink: 0;
}

.skeleton-main {
  flex: 1 1 auto;
  background: var(--skeleton-bg, #202023);
  min-height: 0;
}

/* Responsive */
@media (max-width: 768px) {
  .skeleton-layout {
    flex-direction: column;
  }

  .skeleton-sidebar {
    width: 100%;
    height: 60px;
  }
}
