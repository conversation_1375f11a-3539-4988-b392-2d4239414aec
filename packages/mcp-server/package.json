{"name": "@gauzy/mcp-server", "version": "0.1.0", "description": "Ever Gauzy Platform MCP Server module", "author": {"name": "Ever Co. LTD", "email": "<EMAIL>", "url": "https://ever.co"}, "repository": {"type": "git", "url": "https://github.com/ever-co/ever-gauzy", "directory": "packages/mcp-server"}, "bugs": {"url": "https://github.com/ever-co/ever-gauzy/issues"}, "private": true, "type": "commonjs", "main": "./src/index.js", "types": "./src/index.d.ts", "homepage": "https://ever.co", "license": "AGPL-3.0", "scripts": {"start": "nx serve mcp", "lib:build": "yarn nx build mcp-server", "lib:build:prod": "cross-env NODE_ENV=production yarn ts-node scripts/replace-env-files.ts --environment=prod && yarn nx build mcp-server", "lib:watch": "yarn nx build mcp-server --watch"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.13.1", "@nestjs/common": "^11.1.0", "@nestjs/core": "^11.1.0", "@nestjs/platform-express": "^11.1.0", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "axios": "^1.7.0", "zod": "^3.25.67", "ws": "^8.18.3", "tslib": "^2.3.0"}, "devDependencies": {"@types/jest": "29.5.14", "@types/node": "^20.14.9", "@types/express": "^5.0.3", "@types/cors": "^2.8.19", "@types/cookie-parser": "^1.4.9", "@types/ws": "^8.18.1", "cross-env": "^7.0.3", "typescript": "^5.8.3"}, "keywords": ["api", "mcp", "server", "gauzy", "mcp-server"], "engines": {"node": ">=20", "yarn": ">=1.22"}, "sideEffects": false}