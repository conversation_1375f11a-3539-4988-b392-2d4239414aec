import {
	AccountingTemplate,
	Activity,
	ActivityLog,
	ApiCallLog,
	AppointmentEmployee,
	ApprovalPolicy,
	AvailabilitySlot,
	Candidate,
	CandidateCriterionsRating,
	CandidateDocument,
	CandidateEducation,
	CandidateExperience,
	CandidateFeedback,
	CandidateInterview,
	CandidateInterviewers,
	CandidatePersonalQualities,
	CandidateSkill,
	CandidateSource,
	CandidateTechnologies,
	Comment,
	Contact,
	Country,
	Currency,
	CustomSmtp,
	DailyPlan,
	Dashboard,
	DashboardWidget,
	Deal,
	EmailHistory,
	EmailReset,
	EmailTemplate,
	Employee,
	EmployeeAppointment,
	EmployeeAvailability,
	EmployeeAward,
	EmployeeLevel,
	EmployeePhone,
	EmployeeRecentVisit,
	EmployeeRecurringExpense,
	EmployeeSetting,
	EmployeeNotification,
	EmployeeNotificationSetting,
	Equipment,
	EquipmentSharing,
	EquipmentSharingPolicy,
	EstimateEmail,
	EventType,
	Expense,
	ExpenseCategory,
	Favorite,
	Feature,
	FeatureOrganization,
	Goal,
	GoalGeneralSetting,
	GoalKPI,
	GoalKPITemplate,
	GoalTemplate,
	GoalTimeFrame,
	ImageAsset,
	ImportHistory,
	ImportRecord,
	Income,
	Integration,
	IntegrationEntitySetting,
	IntegrationEntitySettingTied,
	IntegrationMap,
	IntegrationSetting,
	IntegrationTenant,
	IntegrationType,
	Invite,
	Invoice,
	InvoiceEstimateHistory,
	InvoiceItem,
	IssueType,
	KeyResult,
	KeyResultTemplate,
	KeyResultUpdate,
	Language,
	Mention,
	Merchant,
	Organization,
	OrganizationAward,
	OrganizationContact,
	OrganizationDepartment,
	OrganizationDocument,
	OrganizationEmploymentType,
	OrganizationLanguage,
	OrganizationPosition,
	OrganizationProject,
	OrganizationProjectEmployee,
	OrganizationProjectModule,
	OrganizationProjectModuleEmployee,
	OrganizationRecurringExpense,
	OrganizationSprintEmployee,
	OrganizationSprintTask,
	OrganizationSprintTaskHistory,
	OrganizationSprint,
	OrganizationTaskSetting,
	OrganizationTeam,
	OrganizationTeamEmployee,
	OrganizationTeamJoinRequest,
	OrganizationVendor,
	PasswordReset,
	Payment,
	Pipeline,
	PipelineStage,
	Product,
	ProductCategory,
	ProductCategoryTranslation,
	ProductOption,
	ProductOptionGroup,
	ProductOptionGroupTranslation,
	ProductOptionTranslation,
	ProductTranslation,
	ProductType,
	ProductTypeTranslation,
	ProductVariant,
	ProductVariantPrice,
	ProductVariantSetting,
	Reaction,
	Report,
	ReportCategory,
	ReportOrganization,
	RequestApproval,
	RequestApprovalEmployee,
	RequestApprovalTeam,
	ResourceLink,
	Role,
	RolePermission,
	ScreeningTask,
	Screenshot,
	Skill,
	SocialAccount,
	EntitySubscription,
	Tag,
	TagType,
	Task,
	TaskEstimation,
	TaskLinkedIssue,
	TaskPriority,
	TaskRelatedIssueType,
	TaskSize,
	TaskStatus,
	TaskVersion,
	TaskView,
	TenantApiKey,
	Tenant,
	TenantSetting,
	TimeLog,
	TimeOffPolicy,
	TimeOffRequest,
	Timesheet,
	TimeSlot,
	TimeSlotMinute,
	TimeSlotSession,
	User,
	UserOrganization,
	Warehouse,
	WarehouseProduct,
	WarehouseProductVariant
} from './internal';

export const coreEntities = [
	AccountingTemplate,
	Activity,
	ActivityLog,
	ApiCallLog,
	AppointmentEmployee,
	ApprovalPolicy,
	AvailabilitySlot,
	Candidate,
	CandidateCriterionsRating,
	CandidateDocument,
	CandidateEducation,
	CandidateExperience,
	CandidateFeedback,
	CandidateInterview,
	CandidateInterviewers,
	CandidatePersonalQualities,
	CandidateSkill,
	CandidateSource,
	CandidateTechnologies,
	Comment,
	Contact,
	Country,
	Currency,
	CustomSmtp,
	DailyPlan,
	Dashboard,
	DashboardWidget,
	Deal,
	EmailHistory,
	EmailReset,
	EmailTemplate,
	Employee,
	EmployeeAppointment,
	EmployeeAvailability,
	EmployeeAward,
	EmployeeLevel,
	EmployeePhone,
	EmployeeRecentVisit,
	EmployeeRecurringExpense,
	EmployeeSetting,
	EmployeeNotification,
	EmployeeNotificationSetting,
	Equipment,
	EquipmentSharing,
	EquipmentSharingPolicy,
	EstimateEmail,
	EventType,
	Expense,
	ExpenseCategory,
	Favorite,
	Feature,
	FeatureOrganization,
	Goal,
	GoalGeneralSetting,
	GoalKPI,
	GoalKPITemplate,
	GoalTemplate,
	GoalTimeFrame,
	ImageAsset,
	ImportHistory,
	ImportRecord,
	Income,
	Integration,
	IntegrationEntitySetting,
	IntegrationEntitySettingTied,
	IntegrationMap,
	IntegrationSetting,
	IntegrationTenant,
	IntegrationType,
	Invite,
	Invoice,
	InvoiceEstimateHistory,
	InvoiceItem,
	IssueType,
	KeyResult,
	KeyResultTemplate,
	KeyResultUpdate,
	Language,
	Mention,
	Merchant,
	Organization,
	OrganizationAward,
	OrganizationContact,
	OrganizationDepartment,
	OrganizationDocument,
	OrganizationEmploymentType,
	OrganizationLanguage,
	OrganizationPosition,
	OrganizationProject,
	OrganizationProjectEmployee,
	OrganizationProjectModule,
	OrganizationProjectModuleEmployee,
	OrganizationRecurringExpense,
	OrganizationSprintEmployee,
	OrganizationSprintTask,
	OrganizationSprintTaskHistory,
	OrganizationSprint,
	OrganizationTaskSetting,
	OrganizationTeam,
	OrganizationTeamEmployee,
	OrganizationTeamJoinRequest,
	OrganizationVendor,
	PasswordReset,
	Payment,
	Pipeline,
	PipelineStage,
	Product,
	ProductCategory,
	ProductCategoryTranslation,
	ProductOption,
	ProductOptionGroup,
	ProductOptionGroupTranslation,
	ProductOptionTranslation,
	ProductTranslation,
	ProductType,
	ProductTypeTranslation,
	ProductVariant,
	ProductVariantPrice,
	ProductVariantSetting,
	Reaction,
	Report,
	ReportCategory,
	ReportOrganization,
	RequestApproval,
	RequestApprovalEmployee,
	RequestApprovalTeam,
	ResourceLink,
	Role,
	RolePermission,
	ScreeningTask,
	Screenshot,
	Skill,
	SocialAccount,
	EntitySubscription,
	Tag,
	TagType,
	Task,
	TaskEstimation,
	TaskLinkedIssue,
	TaskPriority,
	TaskRelatedIssueType,
	TaskSize,
	TaskStatus,
	TaskVersion,
	TaskView,
	TenantApiKey,
	Tenant,
	TenantSetting,
	TimeLog,
	TimeOffPolicy,
	TimeOffRequest,
	Timesheet,
	TimeSlot,
	TimeSlotMinute,
	TimeSlotSession,
	User,
	UserOrganization,
	Warehouse,
	WarehouseProduct,
	WarehouseProductVariant
];
