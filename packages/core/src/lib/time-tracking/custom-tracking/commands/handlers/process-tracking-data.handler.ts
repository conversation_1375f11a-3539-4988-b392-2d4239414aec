import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>CommandHand<PERSON> } from '@nestjs/cqrs';
import { BadRequestException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { moment } from '../../../../core/moment-extend';
import { decode, Data } from 'clarity-decode';
import { RequestContext } from '../../../../core/context';
import { TimeSlot } from '../../../time-slot/time-slot.entity';
import { TimeSlotSession } from '../../../time-slot-session/time-slot-session.entity';
import { ProcessTrackingDataCommand } from '../process-tracking-data.command';
import { prepareSQLQuery as p } from '../../../../database/database.helper';
import { ICustomActivity, ITrackingSession, ITrackingPayload, JsonData } from '@gauzy/contracts';

@CommandHandler(ProcessTrackingDataCommand)
export class ProcessTrackingDataHandler implements ICommandHandler<ProcessTrackingDataCommand> {
	private readonly logger = new Logger(ProcessTrackingDataHandler.name);

	constructor(
		@InjectRepository(TimeSlot)
		private readonly timeSlotRepository: Repository<TimeSlot>,
		@InjectRepository(TimeSlotSession)
		private readonly timeSlotSessionRepository: Repository<TimeSlotSession>
	) {}

	async execute(command: ProcessTrackingDataCommand): Promise<{
		success: boolean;
		sessionId: string;
		timeSlotId: string;
		message: string;
		session: ITrackingSession | null;
	}> {
		const { input } = command;
		const { payload, startTime } = input;

		if (typeof payload !== 'string' || payload.trim().length === 0) {
			throw new BadRequestException('Payload must be a non-empty string');
		}

		try {
			// Get context information
			const tenantId = RequestContext.currentTenantId() || input.tenantId;

			// Get organizationId from headers first, then fallback to user context
			const req = RequestContext.currentRequest();
			let organizationId = (req?.headers?.['organization-id'] as string) || input.organizationId;

			// Fallback to user's organization if not in headers
			if (!organizationId) {
				const currentUser = RequestContext.currentUser();
				organizationId = currentUser?.employee?.organizationId;
			}

			if (!tenantId || !organizationId) {
				throw new BadRequestException('Tenant and Organization contexts is required');
			}

			// Get user context - could be employee or other user types
			let employeeId = input.employeeId;
			if (!employeeId) {
				// Try to get from current context
				const currentUser = RequestContext.currentUser();
				if (currentUser?.employee?.id) {
					employeeId = currentUser.employee.id;
				} else {
					const ctxEmpId = RequestContext.currentEmployeeId();
					if (ctxEmpId) employeeId = ctxEmpId;
				}
			}

			if (!employeeId) {
				throw new BadRequestException('Employee context is required for tracking data');
			}

			// Decode the payload to extract session information
			const { sessionId, timestampMs, decodedData } = await this.decodeTrackingPayload(payload);

			// Use provided startTime or extracted timestamp
			const trackingTime = startTime ? new Date(startTime) : new Date(timestampMs);

			// Find or create appropriate TimeSlot
			const timeSlot = await this.findOrCreateTimeSlot(employeeId, organizationId, tenantId, trackingTime);

			// Update TimeSlot with tracking data (store both encoded and decoded data)
			const sessionData = await this.updateTimeSlotWithTrackingData(
				timeSlot,
				sessionId,
				payload,
				decodedData,
				trackingTime,
				employeeId,
				tenantId,
				organizationId
			);

			// Return the complete session data
			return {
				success: true,
				sessionId,
				timeSlotId: timeSlot.id,
				message: 'Tracking data processed successfully',
				session: sessionData
			};
		} catch (error) {
			this.logger.error('Failed to process tracking data', error.stack);
			throw new BadRequestException(`Failed to process tracking data: ${error.message}`);
		}
	}

	/**
	 * Decode tracking payload to extract session information
	 */
	private async decodeTrackingPayload(
		payload: string
	): Promise<{ sessionId: string; timestampMs: number; decodedData: Data.DecodedPayload | null }> {
		try {
			const decodedData: Data.DecodedPayload = decode(payload);

			// Extract session ID and timestamp from decoded data
			const sessionId = decodedData.envelope.sessionId;
			const ts = (decodedData as any).timestamp;
			const timestampMs = ts > 1e12 ? ts : ts * 1000;
			return { sessionId, timestampMs, decodedData };
		} catch (error) {
			// Fallback if decoding fails
			return {
				sessionId: `fallback-session-${Date.now()}`,
				timestampMs: Date.now(),
				decodedData: null
			};
		}
	}

	/**
	 * Find existing TimeSlot or create new one for the tracking time
	 */
	private async findOrCreateTimeSlot(
		employeeId: string,
		organizationId: string,
		tenantId: string,
		trackingTime: Date
	): Promise<TimeSlot> {
		// Calculate 10-minute slot boundary
		const slotStart = this.floorToTenMinutes(moment(trackingTime)).toDate();

		let timeSlot: TimeSlot;
		try {
			// Try to find existing TimeSlot
			const query = this.timeSlotRepository.createQueryBuilder('time_slot');

			query.where(p(`"${query.alias}"."tenantId" = :tenantId`), { tenantId });
			query.andWhere(p(`"${query.alias}"."organizationId" = :organizationId`), { organizationId });
			query.andWhere(p(`"${query.alias}"."employeeId" = :employeeId`), { employeeId });
			query.andWhere(p(`"${query.alias}"."startedAt" = :startedAt`), { startedAt: slotStart });

			timeSlot = await query.getOne();
		} catch (error) {
			this.logger.warn(`Error finding TimeSlot: ${error.message}`);
			timeSlot = null;
		}

		// Create new TimeSlot if not found
		if (!timeSlot) {
			const entity = this.timeSlotRepository.create({
				employeeId,
				organizationId,
				tenantId,
				startedAt: slotStart,
				duration: 0,
				keyboard: 0,
				mouse: 0,
				overall: 0,
				customActivity: {}
			});
			timeSlot = await this.timeSlotRepository.save(entity as TimeSlot);
		}

		return timeSlot;
	}

	/**
	 * Round a moment date to the nearest 10 minutes
	 */
	private floorToTenMinutes(date: moment.Moment): moment.Moment {
		const minutes = date.minutes();
		return date
			.minutes(minutes - (minutes % 10))
			.seconds(0)
			.milliseconds(0);
	}

	/**
	 * Get complete session data across all TimeSlots with the same sessionId
	 * Uses the new TimeSlotSession table for efficient lookup with time range filter
	 */
	private async getCompleteSessionData(
		sessionId: string,
		employeeId: string,
		organizationId: string,
		tenantId: string
	): Promise<ITrackingSession | null> {
		// Find all TimeSlots that contain this sessionId using the mapping table
		// PERFORMANCE FIX: Add time range filter to prevent loading millions of records
		// This addresses the issue where querying by sessionId without time constraints
		// could load ALL timeslots for an employee, potentially millions of records
		// Default to last 24 hours as sessions typically don't span longer than this
		const defaultStartDate = new Date(Date.now() - 24 * 60 * 60 * 1000);
		const defaultEndDate = new Date();

		const timeSlotSessions = await this.timeSlotSessionRepository
			.createQueryBuilder('tss')
			.leftJoinAndSelect('tss.timeSlot', 'timeSlot')
			.where('tss.sessionId = :sessionId', { sessionId })
			.andWhere('tss.employeeId = :employeeId', { employeeId })
			.andWhere('tss.organizationId = :organizationId', { organizationId })
			.andWhere('tss.tenantId = :tenantId', { tenantId })
			.andWhere('tss.createdAt BETWEEN :startDate AND :endDate', {
				startDate: defaultStartDate,
				endDate: defaultEndDate
			})
			.orderBy('tss.createdAt', 'ASC')
			.getMany();

		if (timeSlotSessions.length === 0) {
			return null;
		}

		// Extract and merge all payloads for this sessionId
		let allPayloads: ITrackingPayload[] = [];
		let sessionInfo: Omit<ITrackingSession, 'payloads'> | null = null;

		for (const timeSlotSession of timeSlotSessions) {
			const timeSlot = timeSlotSession.timeSlot;
			const customActivity = timeSlot.customActivity as ICustomActivity;
			if (!customActivity?.trackingSessions) continue;

			const session = customActivity.trackingSessions.find((s: ITrackingSession) => s.sessionId === sessionId);
			if (session) {
				if (!sessionInfo) {
					sessionInfo = {
						sessionId: session.sessionId,
						startTime: session.startTime,
						lastActivity: session.lastActivity,
						createdAt: session.createdAt,
						updatedAt: session.updatedAt
					};
				}
				// Merge payloads from this TimeSlot
				allPayloads = [...allPayloads, ...session.payloads];
			}
		}

		if (!sessionInfo) {
			return null;
		}

		// Sort payloads by timestamp
		allPayloads.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());

		return {
			...sessionInfo,
			payloads: allPayloads
		};
	}

	/**
	 * Update TimeSlot with tracking data (store both encoded and decoded data)
	 */
	private async updateTimeSlotWithTrackingData(
		timeSlot: TimeSlot,
		sessionId: string,
		encodedPayload: string,
		decodedData: Data.DecodedPayload | null,
		timestamp: Date,
		employeeId: string,
		tenantId: string,
		organizationId: string
	): Promise<ITrackingSession | null> {
		// Initialize customActivity if it doesn't exist or ensure trackingSessions exists
		const customActivity: ICustomActivity = {
			trackingSessions: []
		};

		// If timeSlot already has customActivity, preserve existing trackingSessions
		if (timeSlot.customActivity) {
			const existingActivity = timeSlot.customActivity as ICustomActivity;
			if (existingActivity.trackingSessions && Array.isArray(existingActivity.trackingSessions)) {
				customActivity.trackingSessions = existingActivity.trackingSessions;
			}
		}

		// Find existing session or create new one
		let existingSession = customActivity.trackingSessions.find(
			(session: ITrackingSession) => session.sessionId === sessionId
		);

		const payload: ITrackingPayload = {
			timestamp: timestamp.toISOString(),
			encodedData: encodedPayload,
			decodedData: decodedData || undefined
		};

		if (existingSession) {
			// Update existing session
			existingSession.payloads.push(payload);
			existingSession.updatedAt = new Date().toISOString();
			existingSession.lastActivity = timestamp.toISOString();
		} else {
			// Create new session
			existingSession = {
				sessionId,
				startTime: timestamp.toISOString(),
				lastActivity: timestamp.toISOString(),
				createdAt: new Date().toISOString(),
				updatedAt: new Date().toISOString(),
				payloads: [payload]
			};
			customActivity.trackingSessions.push(existingSession);
		}

		// Update TimeSlot
		await this.timeSlotRepository.update(timeSlot.id, {
			customActivity: customActivity as JsonData
		});

		// Create or update TimeSlotSession mapping entry
		await this.createOrUpdateTimeSlotSession(
			sessionId,
			timeSlot.id,
			employeeId,
			tenantId,
			organizationId,
			timestamp
		);

		// Return complete session data
		return await this.getCompleteSessionData(sessionId, employeeId, organizationId, tenantId);
	}

	/**
	 * Create or update TimeSlotSession mapping entry
	 */
	private async createOrUpdateTimeSlotSession(
		sessionId: string,
		timeSlotId: string,
		employeeId: string,
		tenantId: string,
		organizationId: string,
		timestamp: Date
	): Promise<void> {
		// Check if mapping already exists
		const existingMapping = await this.timeSlotSessionRepository.findOne({
			where: {
				sessionId,
				timeSlotId,
				tenantId,
				organizationId
			}
		});

		if (!existingMapping) {
			// Create new mapping
			const timeSlotSession = this.timeSlotSessionRepository.create({
				sessionId,
				timeSlotId,
				employeeId,
				tenantId,
				organizationId,
				startTime: timestamp,
				lastActivity: timestamp
			});

			await this.timeSlotSessionRepository.save(timeSlotSession);
		} else {
			// Update last activity
			await this.timeSlotSessionRepository.update(existingMapping.id, {
				lastActivity: timestamp
			});
		}
	}
}
