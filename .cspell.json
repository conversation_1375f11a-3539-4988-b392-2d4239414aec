{"version": "0.2", "language": "en", "$schema": "https://raw.githubusercontent.com/streetsidesoftware/cspell/main/cspell.schema.json", "words": ["activitywatch", "adminer", "activepieces", "ActivePieces", "AGPL", "airbyte", "Akveo", "<PERSON><PERSON>", "alish", "<PERSON><PERSON>", "allcaps", "ALLUSERSPROFILE", "allwindows", "apicivo", "a<PERSON>w", "apida", "apidemo", "apidemocivo", "apidemocw", "apidemoda", "apistagecivo", "apistagecw", "apistageda", "appbaseio", "appcivo", "appcw", "APPDATA", "appleboy", "appplatform", "Artboard", "asar", "ASPECTO", "asymmetrik", "autobuild", "Autobuild", "automake", "backgrounding", "badal", "basicstyles", "billrate", "binutils", "bmap", "browserslist", "Buildable", "buildjet", "buildx", "Buildx", "<PERSON><PERSON>", "cacheable", "camelcase", "<PERSON><PERSON>", "chartjs", "CHATWOOT", "checkmark", "chetan", "Chetan", "childout", "<PERSON><PERSON><PERSON><PERSON>", "circlon", "Civo", "CIVO", "ckeditor", "cloc", "Clokr", "Cloudinary", "CLOUDINARY", "COBERTURA", "Codacy", "codecov", "Codegen", "Codementor", "codeql", "combobox", "commitlint", "Commentable", "commentable", "COMMONPROGRAMFILES", "compat", "compodoc", "concate", "configurator", "containerd", "conv", "copyfiles", "coreweave", "CoreWeave", "cpus", "cqrs", "Crismon", "cubejs", "CUBEJS", "cust", "datatables", "Datepicker", "daterangepicker", "datetime", "datname", "daygrid", "dbname", "DDTHH", "dearmor", "<PERSON><PERSON><PERSON>", "democivo", "democw", "Des<PERSON>rro", "devkit", "<PERSON><PERSON>", "Distributables", "distributables", "DOCKERHUB", "DOCR", "doctl", "downvotes", "dpkg", "d<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "echart", "echarts", "editorplaceholder", "elif", "empl", "entrypoint", "envalid", "envsubst", "EOSQL", "esbuild", "eventnative", "everco", "Evereq", "exif", "EXTP", "ezkfxg", "Fargate", "fastify", "favoritable", "fbstate", "fesm", "fieldname", "filepond", "FIVERR", "flexbox", "<PERSON>lickr", "flyctl", "fontawesome", "fontface", "forin", "formcontrol", "formcontrolname", "formpicker", "fortawesome", "Freemont", "fullcalendar", "Fullname", "fullscreenable", "fulltext", "gauzydesktop", "gauzydesktoptimer", "gau<PERSON><PERSON><PERSON><PERSON><PERSON>", "gau<PERSON><PERSON><PERSON><PERSON><PERSON>", "gauzyserver", "GHCUP", "g<PERSON><PERSON><PERSON><PERSON>", "Gitter", "<PERSON><PERSON>dy", "googlemaps", "GOROOT", "<PERSON><PERSON>", "graphicsmagick", "gridcell", "grpc", "Guazy", "gauzymcpserver", "Harb<PERSON>", "healthcheck", "honeycombio", "Hoster", "hubstaff", "HUBSTAFF", "huntr", "IAWS", "ibase", "ichart", "icns", "icnsutils", "iconed", "icrud", "ienvironment", "IKPI", "ILIKE", "ilinked", "immer", "initdb", "inotify", "instantane<PERSON>us", "INTERTRX", "INTERVALSRVCE", "INTERVTRX", "inversed", "ionicons", "IPCCIS", "IPCINIT", "IPCLS", "IPCNOTIF", "IPCNTFSHOT", "IPCPERM", "IPCQCI", "IPCQNVGLOGIN", "IPCQSAVEIMG", "IPCQSHEET", "IPCQSLOT", "IPCQWIN", "IPCREFRESH", "IPCRESTORE", "IPCRMAFK", "IPCRMAW", "IPCRMSLOT", "IPCRMUSER", "IPCRMWK", "IPCRTNSLOT", "IPCSAVEINTER", "IPCSAVESHOT", "IPCSAVESLOT", "IPCSTARTMR", "IPCSTOPTMR", "IPCTKSCAPTURE", "IPCTKSHOT", "IPCUPDATESYNCINTERVAL", "IPCUPDATESYNCTMR", "IPCWS", "iquery", "iqueue", "iremote", "ISMTP", "itimer", "<PERSON>uben<PERSON>", "iubgreen", "IURL", "iwindow", "jasminewd", "javascripts", "<PERSON><PERSON>", "Jen<PERSON>file", "jitsu", "Jitsu", "jitsucom", "<PERSON><PERSON><PERSON>", "juli<PERSON>y", "kafka<PERSON>s", "KEYCLOAK", "KEYMETRICS", "keyout", "key<PERSON>ult", "keyresults", "keyrings", "killall", "KNEX", "knexfile", "<PERSON><PERSON><PERSON>s", "knowledeg", "Konviser", "KPIS", "kube", "kubeconfig", "kubectl", "Kubernetes", "kurkle", "<PERSON><PERSON><PERSON>", "Las<PERSON>", "latlng", "letsencrypt", "libaom", "libappindicator", "libgcc", "lib<PERSON><PERSON>", "libsql", "libstdc", "libvpx", "libx", "libx264", "linebreak", "linkedin", "linklocal", "listbox", "localforage", "longform", "longtext", "macbook", "MAINDB", "MAINLOADURL", "MAINOPENEXT", "MAINSTRSERVER", "MAINUNEXCEPTION", "MAINUPDTABORT", "MAINWB", "MAINWININIT", "masuk", "<PERSON><PERSON><PERSON><PERSON>", "maximizable", "maxlength", "memlock", "Metadatas", "metatype", "microservices", "mikro", "<PERSON><PERSON><PERSON>", "MikroOrm", "MikroORM", "<PERSON><PERSON>", "minio", "MINIO", "minizlib", "minte", "mjml", "modelcontextprotocol", "msedge", "MSYS", "<PERSON><PERSON>", "napi", "nasm", "nats", "nbbutton", "nbinput", "ncipollo", "<PERSON><PERSON><PERSON>", "nestjsx", "newkey", "ngcc", "ngdevtools", "nginx", "ngneat", "ngsw", "ngtools", "nocase", "nodownload", "NOLOGO", "notif", "npmignore", "nrwl", "nsis", "nstudio", "ntegral", "NTSC", "openai", "OPENAI", "openapi", "openfromhere", "opensearch", "opentelemetry", "orgname", "Orgs", "originalname", "ormconfig", "ormlogs", "OTEL", "OTLP", "oxsecurity", "<PERSON><PERSON>r", "pagedata", "pageobjects", "pantone", "<PERSON><PERSON><PERSON>", "parentin", "payperiod", "pdfmake", "pdfmaker", "Pdfmaker", "makecom", "PERFLOG", "PGBIN", "pgcrypto", "PGDATA", "PGPASSWORD", "pgrep", "PGROOT", "pgsql", "PGUSER", "pgweb", "Pgweb", "PHPROOT", "piecelabel", "pinghost", "pino", "PIPX", "policyicon", "postbuild", "postgre", "postgres", "postmarkapp", "prebuild", "preload", "presigner", "prestart", "pricetags", "PRKILL", "PRMIG", "Probot", "PROGRAMFILES", "PROJECTRX", "proto", "psql", "pulumi", "<PERSON><PERSON><PERSON>", "Qube", "Quer", "r_emailaddress", "r_liteprofile", "rahul", "<PERSON><PERSON>", "raleway", "randomcolor", "rangepicker", "RDCL", "<PERSON><PERSON>", "rediss", "reference", "Rememberd", "rememberMe", "Repobeats", "repos", "resave", "rfdc", "roboto", "<PERSON><PERSON><PERSON>", "sarif", "SARIF", "SCALEWAY", "<PERSON><PERSON><PERSON>", "screenfull", "scriptable", "scrollbars", "scrollblock", "scrollgrid", "Seedable", "Segoe", "serializables", "serverapi", "server-mcp", "servermcp", "serverMCP", "setuid", "setuptools", "Shaked", "Sharings", "siderbar", "signin", "<PERSON><PERSON>", "signups", "slnt", "sluggable", "Smee", "snyk", "socicon", "solidvar", "sonarqube", "Sonarscanner", "so<PERSON>off", "Sqls", "sqlserver", "squirrelly", "sslmode", "SSSSZ", "stackoverflow", "stagecivo", "stagecw", "s<PERSON><PERSON><PERSON><PERSON><PERSON>l", "streamifier", "stylelint", "<PERSON><PERSON><PERSON>", "superfly", "swapoff", "swatchbook", "swiper", "SXGA", "tabid", "Tabset", "takeshot", "TASKTRX", "theora", "Theora", "thmb", "timeframe", "timegrid", "TIMELOG", "timelogs", "Timeoff", "TIMERTRX", "TIMERTRXER", "TIMERTRXERUPDATE", "Timesheet", "timesheets", "Timesheets", "timeslot", "timeslots", "TIMESTAMPDIFF", "timestamptz", "titlebar", "titlecase", "TMRSRVCE", "toastr", "togglefullscreen", "TOOLSDIRECTORY", "traefik", "trasp", "Trendshift", "<PERSON><PERSON><PERSON>", "trufflehog", "trufflesecurity", "tsbuildinfo", "tsconfigs", "twing", "typeahead", "typeorm", "typeOrm", "typeORM", "Udemy", "ulimits", "unassigning", "unassignment", "undici", "UNINVOICED", "uniquelocal", "unixepoch", "UNPROCESSABLE", "Unregisters", "unsubmit", "unzipper", "UPDATECHECK", "uploader", "upvotes", "upwork", "Upwork", "UPWORK", "urlpath", "USERSRVCE", "USERTRX", "uuidv4", "UXGA", "vcpu", "VMFE", "VULTR", "Wakatime", "wasabi<PERSON>s", "wbars", "wdth", "webapp", "websockets", "wght", "Whitespaces", "woot", "workdiary", "Workdiary", "wscat", "WXGA", "xaxis", "xpack", "xplat", "xplatframework", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "zfgk", "zipkin", "gauzyapiserver", "Embeddable", "locutus", "proto", "<PERSON>uben<PERSON>", "iconed", "cust", "trasp", "policyicon", "wbars", "allcaps", "iubgreen", "unixepoch", "macbook", "upvotes", "downvotes", "<PERSON><PERSON>", "<PERSON><PERSON>", "Clokr", "KNEX", "ASPECTO", "COBERTURA", "EXTP", "RDCL", "VMFE", "GOROOT", "PGBIN", "PGDATA", "PGPASSWORD", "PGROOT", "PGUSER", "PHPROOT", "PIPX", "UPDATECHECK", "NOLOGO", "GHCUP", "MSYS", "PERFLOG", "PROGRAMFILES", "ALLUSERSPROFILE", "APPDATA", "COMMONPROGRAMFILES", "serverapi", "serializables", "d<PERSON><PERSON><PERSON>", "titlebar", "raleway", "browserslist", "flexbox", "nbinput", "nbbutton", "xaxis", "wdth", "concate", "typeahead", "maxlength", "xplatframework", "localforage", "longform", "rfdc", "bluehalo", "libx264", "libx", "WXGA", "SXGA", "UXGA", "NTSC", "libvpx", "libaom", "theora", "Theora", "Unregisters", "allwindows", "<PERSON><PERSON><PERSON>", "minizlib", "DOCR", "apida", "appplatform", "apistageda", "elif", "appleboy", "apidemoda", "forin", "distutils", "loggable", "inversed", "iquery", "nodownload", "Zrdm", "greenkeeper", "classpath", "isready", "openzipkin", "opensearchproject", "nofile", "nosniff", "ICDN", "INPM", "ɵcmp", "ɵmod", "ɵcreate", "notarytool", "evergauzyagent", "electronmon", "apistage", "<PERSON><PERSON><PERSON>", "uninstallation", "iohook", "tsfn", "hhook", "lresult", "wparam", "lparam", "refcon", "cflags", "uiohook", "gauzyagent", "PostHog", "posthog", "PKCE", "udts", "autocapture", "pageview", "signup", "wrapp", "dyld", "oidc", "Registerables", "Archs", "camshot", "camshots", "dtos", "soundshot", "soundshots", "runtimes"], "useGitignore": true, "ignorePaths": [".deploy/*", ".git/*", ".git/!{COMMIT_EDITMSG,EDITMSG}", ".git/*/**", ".yarn", "**/*.jar", ".pnp.js", "**/.git/**", ".vscode", ".giti<PERSON>re", "action/lib/**", "coverage", ".cspell.json", "cspell.json", "__snapshots__", "__recordings__", "**/coverage/**", "**/fixtures/**/*.json", "**/fixtures/sampleCode/*errors/", "**/node_modules/**", "**/vscode-extension/**", "package-lock.json", "yarn.lock", "**/assets/i18n/*.json", "**/migrations/**", "packages/**/*.seed.json", "**/*.svg", "**/*.mjml", "tools/build/webpack.config.js", "docker-compose.demo.yml", "docker-compose.yml", "**/agent/src/assets/icons/**", "**/agent/src/assets/images/**", "./qodo/**", "**/rules", ".codacy"]}